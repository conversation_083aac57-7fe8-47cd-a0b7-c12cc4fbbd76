import { TranslationKeys } from '../types';

export const enTranslations: TranslationKeys = {
  fileUpload: {
    title: 'Upload CSV File',
    beforeYouStart: 'Before you start',
    instructions: {
      prepareCsv: 'Prepare your CSV file with semicolon (;) separators',
      ensureUtf8: 'Ensure UTF-8 encoding',
      includeHeaders: 'Include all required headers',
      downloadTemplate: 'Download the template below for the correct format'
    },
    downloadTemplate: 'Download CSV Template',
    dropZone: {
      drop: 'Drop your CSV file here',
      browse: 'Drop your CSV file here, or click to browse',
      maxSize: 'Maximum file size: 10MB',
      supportedFormat: 'Supported format: CSV files with semicolon separators',
      processing: 'Processing file...'
    },
    fileError: 'File Error',
    requirements: {
      title: 'File Requirements:',
      format: 'File format: CSV (.csv)',
      encoding: 'Encoding: UTF-8',
      separator: 'Separator: Semicolon (;)',
      requiredFields: 'Required fields: Laufendenr',
      maxSize: 'Maximum size: 10MB'
    }
  },

  preview: {
    title: 'Data Preview',
    dataPreview: 'Data Preview',
    foundRowsWithColumns: 'Found {rows} rows with {columns} columns',
    showAllColumns: 'Show all columns',
    showLessColumns: 'Show less columns',
    pagination: {
      showing: 'Showing',
      of: 'of',
      previous: 'Previous',
      next: 'Next'
    }
  },

  validation: {
    title: 'Data Validation',
    summary: 'Summary',
    tabs: {
      summary: 'Summary',
      errors: 'Errors',
      warnings: 'Warnings',
      conflicts: 'Conflicts'
    },
    stats: {
      totalRows: 'Total rows',
      validRows: 'Valid rows',
      errors: 'Errors',
      warnings: 'Warnings',
      conflicts: 'Conflicts'
    },
    validationIssues: 'Validation Issues',
    conflictResolution: {
      title: 'Conflict Resolution',
      existsMessage: 'already exists',
      existingRecord: 'Existing record:',
      created: 'Created:',
      actions: {
        skip: 'Skip',
        update: 'Update',
        createNew: 'Create new version'
      }
    },
    cannotProceed: 'Cannot proceed with import due to errors. Please fix the errors and try again.',
    proceedAnyway: 'Proceed anyway despite warnings'
  },

  import: {
    title: 'Import Data',
    importing: 'Importing data...',
    progress: 'Progress',
    steps: {
      validating: 'Validating data',
      checkingConflicts: 'Checking for conflicts',
      creatingRecords: 'Creating records',
      finalizing: 'Finalizing import'
    }
  },

  progress: {
    steps: {
      upload: {
        label: 'File Upload',
        description: 'Select CSV file'
      },
      preview: {
        label: 'Preview',
        description: 'Review data'
      },
      validate: {
        label: 'Validate',
        description: 'Check for errors'
      },
      import: {
        label: 'Import',
        description: 'Process data'
      },
      complete: {
        label: 'Complete',
        description: 'Finished'
      }
    }
  },

  errors: {
    validationIssues: 'Validation Issues',
    filterBySeverity: 'Filter by severity:',
    all: 'All',
    error: 'Error',
    warning: 'Warning',
    showLess: 'Show Less',
    showMore: 'Show More',
    row: 'Row',
    field: 'Field',
    value: 'Value'
  },

  common: {
    buttons: {
      previous: 'Previous',
      next: 'Next',
      cancel: 'Cancel',
      continue: 'Continue',
      finish: 'Finish',
      retry: 'Retry',
      close: 'Close'
    },
    messages: {
      success: 'Success',
      error: 'Error',
      warning: 'Warning',
      info: 'Info',
      processing: 'Processing...',
      completed: 'Completed'
    }
  },

  toasts: {
    fileParsed: 'File parsed successfully',
    parseError: 'Parse Error',
    importSuccess: 'Import Successful',
    importError: 'Import Error',
    importCompleted: 'Import completed successfully',
    importCompletedWithErrors: 'Import completed with errors'
  },

  page: {
    title: 'CSV Import',
    description: 'Import deportation data from a CSV file',
    startImport: 'Start Import',
    backToTable: 'Back to Table'
  }
};
