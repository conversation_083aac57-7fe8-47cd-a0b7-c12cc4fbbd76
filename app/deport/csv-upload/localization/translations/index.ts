import { Language, TranslationKeys } from '../types';
import { deTranslations } from './de';
import { enTranslations } from './en';

export const translations: Record<Language, TranslationKeys> = {
  de: deTranslations,
  en: enTranslations
};

export const defaultLanguage: Language = 'de'; // German as default
export const supportedLanguages: Language[] = ['de', 'en'];

export function getTranslation(language: Language): TranslationKeys {
  return translations[language] || translations[defaultLanguage];
}

export function isValidLanguage(lang: string): lang is Language {
  return supportedLanguages.includes(lang as Language);
}
