import { TranslationKeys } from '../types';

export const deTranslations: TranslationKeys = {
  fileUpload: {
    title: 'CSV-Datei hochladen',
    beforeYouStart: 'Bevor Sie beginnen',
    instructions: {
      prepareCsv: 'Bereiten Sie Ihre CSV-Datei mit Semikolon (;) als Trennzeichen vor',
      ensureUtf8: 'Stellen Sie UTF-8-Kodierung sicher',
      includeHeaders: 'Fügen Sie alle erforderlichen Spaltenüberschriften hinzu',
      downloadTemplate: 'Laden Sie die Vorlage unten für das korrekte Format herunter'
    },
    downloadTemplate: 'CSV-Vorlage herunterladen',
    dropZone: {
      drop: 'CSV-Datei hier ablegen',
      browse: 'CSV-Datei hier ablegen oder zum Durchsuchen klicken',
      maxSize: 'Maximale Dateigröße: 10MB',
      supportedFormat: 'Unterstütztes Format: CSV-Dateien mit Semikolon-Trennzeichen',
      processing: 'Datei wird verarbeitet...'
    },
    fileError: 'Date<PERSON>hler',
    requirements: {
      title: 'Dateianforderungen:',
      format: 'Dateiformat: CSV (.csv)',
      encoding: 'Kodierung: UTF-8',
      separator: 'Trennzeichen: Semikolon (;)',
      requiredFields: 'Pflichtfelder: Laufendenr',
      maxSize: 'Maximale Größe: 10MB'
    }
  },

  preview: {
    title: 'Datenvorschau',
    dataPreview: 'Datenvorschau',
    foundRowsWithColumns: 'Gefunden {rows} Zeilen mit {columns} Spalten',
    showAllColumns: 'Alle Spalten anzeigen',
    showLessColumns: 'Weniger Spalten anzeigen',
    pagination: {
      showing: 'Zeige',
      of: 'von',
      previous: 'Vorherige',
      next: 'Nächste'
    }
  },

  validation: {
    title: 'Datenvalidierung',
    summary: 'Zusammenfassung',
    tabs: {
      summary: 'Zusammenfassung',
      errors: 'Fehler',
      warnings: 'Warnungen',
      conflicts: 'Konflikte'
    },
    stats: {
      totalRows: 'Zeilen insgesamt',
      validRows: 'Gültige Zeilen',
      errors: 'Fehler',
      warnings: 'Warnungen',
      conflicts: 'Konflikte'
    },
    validationIssues: 'Validierungsprobleme',
    conflictResolution: {
      title: 'Konfliktlösung',
      existsMessage: 'bereits vorhanden',
      existingRecord: 'Vorhandener Datensatz:',
      created: 'Erstellt:',
      actions: {
        skip: 'Überspringen',
        update: 'Aktualisieren',
        createNew: 'Neue Version erstellen'
      }
    },
    cannotProceed: 'Import kann aufgrund von Fehlern nicht fortgesetzt werden. Bitte korrigieren Sie die Fehler und versuchen Sie es erneut.',
    proceedAnyway: 'Trotz Warnungen fortfahren'
  },

  import: {
    title: 'Daten importieren',
    importing: 'Importiere Daten...',
    progress: 'Fortschritt',
    steps: {
      validating: 'Daten validieren',
      checkingConflicts: 'Konflikte prüfen',
      creatingRecords: 'Datensätze erstellen',
      finalizing: 'Import abschließen'
    }
  },

  progress: {
    steps: {
      upload: {
        label: 'Datei hochladen',
        description: 'CSV-Datei auswählen'
      },
      preview: {
        label: 'Vorschau',
        description: 'Daten überprüfen'
      },
      validate: {
        label: 'Validieren',
        description: 'Auf Fehler prüfen'
      },
      import: {
        label: 'Importieren',
        description: 'Daten verarbeiten'
      },
      complete: {
        label: 'Abgeschlossen',
        description: 'Fertig'
      }
    }
  },

  errors: {
    validationIssues: 'Validierungsprobleme',
    filterBySeverity: 'Nach Schweregrad filtern:',
    all: 'Alle',
    error: 'Fehler',
    warning: 'Warnung',
    showLess: 'Weniger anzeigen',
    showMore: 'Mehr anzeigen',
    row: 'Zeile',
    field: 'Feld',
    value: 'Wert'
  },

  common: {
    buttons: {
      previous: 'Zurück',
      next: 'Weiter',
      cancel: 'Abbrechen',
      continue: 'Fortfahren',
      finish: 'Fertigstellen',
      retry: 'Wiederholen',
      close: 'Schließen'
    },
    messages: {
      success: 'Erfolgreich',
      error: 'Fehler',
      warning: 'Warnung',
      info: 'Information',
      processing: 'Verarbeitung läuft...',
      completed: 'Abgeschlossen'
    }
  },

  toasts: {
    fileParsed: 'Datei erfolgreich geparst',
    parseError: 'Parse-Fehler',
    importSuccess: 'Import erfolgreich',
    importError: 'Import-Fehler',
    importCompleted: 'Import erfolgreich abgeschlossen',
    importCompletedWithErrors: 'Import mit Fehlern abgeschlossen'
  },

  page: {
    title: 'CSV-Import',
    description: 'Importieren Sie Deportationsdaten aus einer CSV-Datei',
    startImport: 'Import starten',
    backToTable: 'Zurück zur Tabelle'
  }
};
