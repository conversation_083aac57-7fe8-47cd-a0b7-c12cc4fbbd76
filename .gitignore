# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

.roo
/.vscode
package-lock.json

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env
mcp*
.env*
.env
.env.local
*.env*
/.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# contentlayer
.contentlayer
