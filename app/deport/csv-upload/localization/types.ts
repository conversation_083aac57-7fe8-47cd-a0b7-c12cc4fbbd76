// Localization types for CSV upload feature

export type Language = 'de' | 'en';

export interface TranslationKeys {
  // File Upload Step
  fileUpload: {
    title: string;
    beforeYouStart: string;
    instructions: {
      prepareCsv: string;
      ensureUtf8: string;
      includeHeaders: string;
      downloadTemplate: string;
    };
    downloadTemplate: string;
    dropZone: {
      drop: string;
      browse: string;
      maxSize: string;
      supportedFormat: string;
      processing: string;
    };
    fileError: string;
    requirements: {
      title: string;
      format: string;
      encoding: string;
      separator: string;
      requiredFields: string;
      maxSize: string;
    };
  };

  // Preview Step
  preview: {
    title: string;
    dataPreview: string;
    foundRowsWithColumns: string;
    showAllColumns: string;
    showLessColumns: string;
    pagination: {
      showing: string;
      of: string;
      previous: string;
      next: string;
    };
  };

  // Validation Step
  validation: {
    title: string;
    summary: string;
    tabs: {
      summary: string;
      errors: string;
      warnings: string;
      conflicts: string;
    };
    stats: {
      totalRows: string;
      validRows: string;
      errors: string;
      warnings: string;
      conflicts: string;
    };
    validationIssues: string;
    conflictResolution: {
      title: string;
      existsMessage: string;
      existingRecord: string;
      created: string;
      actions: {
        skip: string;
        update: string;
        createNew: string;
      };
    };
    cannotProceed: string;
    proceedAnyway: string;
  };

  // Import Step
  import: {
    title: string;
    importing: string;
    progress: string;
    steps: {
      validating: string;
      checkingConflicts: string;
      creatingRecords: string;
      finalizing: string;
    };
  };

  // Progress Indicator
  progress: {
    steps: {
      upload: {
        label: string;
        description: string;
      };
      preview: {
        label: string;
        description: string;
      };
      validate: {
        label: string;
        description: string;
      };
      import: {
        label: string;
        description: string;
      };
      complete: {
        label: string;
        description: string;
      };
    };
  };

  // Error Display
  errors: {
    validationIssues: string;
    filterBySeverity: string;
    all: string;
    error: string;
    warning: string;
    showLess: string;
    showMore: string;
    row: string;
    field: string;
    value: string;
  };

  // Common buttons and actions
  common: {
    buttons: {
      previous: string;
      next: string;
      cancel: string;
      continue: string;
      finish: string;
      retry: string;
      close: string;
    };
    messages: {
      success: string;
      error: string;
      warning: string;
      info: string;
      processing: string;
      completed: string;
    };
  };

  // Toast messages
  toasts: {
    fileParsed: string;
    parseError: string;
    importSuccess: string;
    importError: string;
    importCompleted: string;
    importCompletedWithErrors: string;
  };

  // Main page
  page: {
    title: string;
    description: string;
    startImport: string;
    backToTable: string;
  };
}
